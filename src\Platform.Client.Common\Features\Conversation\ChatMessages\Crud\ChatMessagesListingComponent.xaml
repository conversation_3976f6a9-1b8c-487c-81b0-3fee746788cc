﻿<?xml version="1.0" encoding="utf-8" ?>
<local:ChatMessagesListingViewBase
    x:Class="Platform.Client.Common.Features.Conversation.ChatMessagesListingView"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:local="clr-namespace:Platform.Client.Common.Features.Conversation"
    xmlns:viewmodels="clr-namespace:Platform.Client.Services.Features.Conversation;assembly=Platform.Client.Services"
    x:DataType="local:ChatMessagesListingView"
    BackgroundColor="White">
    <Shell.TitleView>
        <Grid Padding="0" BackgroundColor="White">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="28" />
                <ColumnDefinition Width="44" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>
            <Grid.GestureRecognizers>
                <TapGestureRecognizer Command="{Binding BackCommand}" />
            </Grid.GestureRecognizers>
            <Image
                HorizontalOptions="Start"
                MaximumHeightRequest="24"
                MaximumWidthRequest="24"
                Source="left.svg" />
            <Border
                Grid.Column="1"
                HeightRequest="36"
                HorizontalOptions="Start"
                WidthRequest="36">
                <Border.StrokeShape>
                    <RoundRectangle CornerRadius="45" />
                </Border.StrokeShape>
                <Image Source="{Binding SelectedChat.Avatar}" />
            </Border>
            <Label
                Grid.Column="2"
                FontAttributes="Bold"
                FontSize="18"
                HorizontalOptions="Start"
                Text="{Binding FriendName}"
                TextColor="#333333"
                VerticalOptions="Center"
                VerticalTextAlignment="Center" />
        </Grid>
    </Shell.TitleView>
    <Shell.BackButtonBehavior>
        <BackButtonBehavior IsVisible="False" />
    </Shell.BackButtonBehavior>
    <!--  Page content goes here  -->
    <ContentPage.Resources>
        <DataTemplate x:Key="DateSeparatorTemplate" x:DataType="viewmodels:DateSeparatorItem">
            <Border
                Margin="8"
                BackgroundColor="Gainsboro"
                HorizontalOptions="Center"
                StrokeShape="RoundRectangle 8"
                StrokeThickness="0"
                VerticalOptions="Start">
                <Label
                    Padding="8"
                    FontAttributes="Bold"
                    FontSize="Small"
                    Text="{Binding Date, StringFormat='{0:dd MMM yyyy}'}"
                    TextColor="#555555" />
            </Border>
        </DataTemplate>
        <DataTemplate x:Key="MessageTemplate" x:DataType="viewmodels:ChatMessagesListingViewModel">
            <VerticalStackLayout>
                <Border
                    Margin="8,4,8,4"
                    Padding="10"
                    BackgroundColor="{Binding BackgroundColor}"
                    HorizontalOptions="{Binding HorizontalOptions}"
                    MaximumWidthRequest="250"
                    MinimumWidthRequest="150"
                    Stroke="#EAEAEA"
                    StrokeShape="{Binding BubbleShape}"
                    StrokeThickness="1">
                    <Grid>
                        <Label
                            FontSize="16"
                            Text="{Binding Content}"
                            TextColor="{Binding MessageColor}" />

                        <Line
                            HeightRequest="1"
                            Stroke="Aqua"
                            StrokeThickness="1" />
                    </Grid>
                </Border>
                <HorizontalStackLayout Margin="12,0,8,8"   HorizontalOptions="{Binding HorizontalOptions}">
                    <Label
                        FontAttributes="Bold"
                        FontSize="11"
                        HorizontalOptions="End"
                        Text="{Binding TimeOfTheDay}"
                        TextColor="{Binding TimestampColor}" />
                    <Image
                        Margin="4,0,0,0"
                        HeightRequest="12"
                        Source="{Binding DeliverySymbol}" />
                </HorizontalStackLayout>
            </VerticalStackLayout>
        </DataTemplate>
        <local:ChatTemplateSelector
            x:Key="ChatTemplateSelector"
            DateSeparatorTemplate="{StaticResource DateSeparatorTemplate}"
            MessageTemplate="{StaticResource MessageTemplate}" />
    </ContentPage.Resources>
    <ContentPage.Content>
        <Border
            BackgroundColor="#F4F4F5"
            Stroke="Gainsboro"
            StrokeThickness="0">
            <Border.StrokeShape>
                <RoundRectangle CornerRadius="16,16,0,0" />
            </Border.StrokeShape>
            <Grid RowDefinitions="*, Auto">
                <CollectionView
                    x:Name="collection"
                    ItemTemplate="{StaticResource ChatTemplateSelector}"
                    ItemsSource="{Binding Items}"
                    ItemsUpdatingScrollMode="KeepLastItemInView"
                    Scrolled="OnCollectionViewScrolled"
                    SizeChanged="CollectionView_SizeChanged" />
                <Grid
                    Padding="8"
                    BackgroundColor="White"
                    Row="1"
                    RowSpacing="0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="44" />
                    </Grid.ColumnDefinitions>
                    <Border
                        Margin="8,0,8,0"
                        BackgroundColor="#F4F4F5"
                        Stroke="#F4F4F5">
                        <Border.StrokeShape>
                            <RoundRectangle CornerRadius="45" />
                        </Border.StrokeShape>
                        <Grid>
                            <Editor
                                Grid.Column="0"
                                Margin="8,0,36,0"
                                Placeholder="Type a message"
                                Text="{Binding SelectedItem.Content, Mode=TwoWay}" />

                            <ImageButton
                                Margin="8,0,8,0"
                                HorizontalOptions="End"
                                MaximumHeightRequest="20"
                                MaximumWidthRequest="20"
                                Source="paperclip_regular.svg" />
                        </Grid>
                    </Border>
                    <ImageButton
                        Grid.Column="1"
                        Padding="12"
                        BackgroundColor="Black"
                        BorderColor="Transparent"
                        Command="{Binding SaveCommand}"
                        CornerRadius="45"
                        MaximumHeightRequest="44"
                        MaximumWidthRequest="44"
                        Source="paper_plane_top_solid.svg" />

                </Grid>
                <Border
                    x:Name="borderDateOverlay"
                    Margin="8"
                    BackgroundColor="Gainsboro"
                    HorizontalOptions="Center"
                    IsVisible="False"
                    StrokeShape="RoundRectangle 8"
                    StrokeThickness="0"
                    VerticalOptions="Start">
                    <Label
                        x:Name="lblDateOverlay"
                        Padding="8"
                        FontAttributes="Bold"
                        FontSize="Small"
                        TextColor="#555555" />
                </Border>
            </Grid>
        </Border>
    </ContentPage.Content>
</local:ChatMessagesListingViewBase>
